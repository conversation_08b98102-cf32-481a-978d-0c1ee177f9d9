#include "math.h"
#include "libs.h"

#include "stm32f1xx_hal_def.h"
#include "stm32f1xx_hal_tim.h"
#include "stm32f103xb.h"

t_Chip Chip = WS2812;

void ARGB_New(ARGB *this, uint16_t NumPixels, uint16_t <PERSON><PERSON><PERSON>, int8_t  <PERSON><PERSON>, TIM_HandleTypeDef <PERSON>, DMA_HandleTypeDef <PERSON><PERSON><PERSON><PERSON><PERSON>, t_DMA_Size DMA_Size)
{
	this->NumPixels  = NumPixels;
	this->Tim<PERSON>and<PERSON> = TimHandler;
	this-><PERSON><PERSON>		 = <PERSON><PERSON>;

	// Timer handler
	switch(<PERSON><PERSON>)
	{
		case TIM_CHANNEL_1:
			this->TIM_DMA_ID = TIM_DMA_ID_CC1;
			this->TIM_DMA_CC = TIM_DMA_CC1;
			this->TIM_CCR = TimHandler.Instance->CCR1; break;
		case TIM_CHANNEL_2:
			this->TIM_DMA_ID = TIM_DMA_ID_CC2;
			this->TIM_DMA_CC = TIM_DMA_CC2;
			this->TIM_CCR = TimHandler.Instance->CCR2; break;
		case TIM_CHANNEL_3:
			this->TIM_DMA_ID = TIM_DMA_ID_CC3;
			this->TIM_DMA_CC = TIM_DMA_CC3;
			this->TIM_CCR = TimHandler.Instance->CCR3; break;
		case TIM_CHANNEL_4:
			this->TIM_DMA_ID = TIM_DMA_ID_CC4;
			this->TIM_DMA_CC = TIM_DMA_CC4;
			this->TIM_CCR = TimHandler.Instance->CCR4; break;
		default:
			for(;;);
	}

	// Timer's RCC Bus
	if(TimNum == 1 || (TimNum >= 8 && TimNum <= 11))
		this->APB = APB1;
	else
		this->APB = APB2;

	// Calculate PWM_BUF_LEN & RGB_BUF Size
	uint64_t NUM_BYTES;
	if(Chip == SK6812)
	{
		NUM_BYTES = 4 * NumPixels;
		this->PWM_BUF_LEN = 4 * 8 * 2;
	}
	else
	{
		NUM_BYTES = 3 * NumPixels;
		this->PWM_BUF_LEN = 3 * 8 * 2;
	}
	this->RGB_BUF = (volatile u8_t *)malloc(NUM_BYTES * sizeof(u8_t));

	// DMA Size,PWM_BUF
	switch(DMA_Size)
	{
		case BYTE:
			this->DMA_Size = malloc(sizeof(uint8_t));
			this->PWM_BUF = malloc(this->PWM_BUF_LEN * sizeof(uint8_t)); break;
		case HWORD:
			this->DMA_Size = malloc(sizeof(uint16_t));
			this->PWM_BUF = malloc(this->PWM_BUF_LEN * sizeof(uint16_t)); break;
		case WORD:
			this->DMA_Size = malloc(sizeof(uint32_t));
			this->PWM_BUF = malloc(this->PWM_BUF_LEN * sizeof(uint32_t)); break;
	}

	// Configure variables
	this->BUF_COUNTER = 0;
	this->ARGB_BR = 255;

}

void ARGB_Init(ARGB *this)
{
	/* Auto-calculation! */
	u32_t APBfq; // Clock freq
	switch(this->APB)
	{
		case APB1:
			APBfq = HAL_RCC_GetPCLK1Freq();
			APBfq *= (RCC->CFGR & RCC_CFGR_PPRE1) == 0 ? 1 : 2; break;
		case APB2:
			APBfq = HAL_RCC_GetPCLK2Freq();
			APBfq *= (RCC->CFGR & RCC_CFGR_PPRE2) == 0 ? 1 : 2; break;
	}

	switch(Chip)
	{
		case WS2811S:
			APBfq /= (uint32_t) (400 * 1000);  // 400 KHz - 2.5us
		default:
			APBfq /= (uint32_t) (800 * 1000);  // 800 KHz - 1.25us
	}

	this->TimHandler.Instance->PSC = 0;							// dummy hardcode now
	this->TimHandler.Instance->ARR = (uint16_t) (APBfq - 1);    // set timer prescaler
	this->TimHandler.Instance->EGR = 1;                         // update timer registers

	switch(WS2811F)
	{
		case WS2811F:
		case WS2811S:
			this->PWM_HI = (u8_t) (APBfq * 0.48) - 1;     		// Log.1 - 48% - 0.60us/1.2us
			this->PWM_LO = (u8_t) (APBfq * 0.20) - 1; break;    // Log.0 - 20% - 0.25us/0.5us
		case WS2812:
			this->PWM_HI = (u8_t) (APBfq * 0.56) - 1;           // Log.1 - 56% - 0.70us
			this->PWM_LO = (u8_t) (APBfq * 0.28) - 1; break;    // Log.0 - 28% - 0.35us
		case SK6812:
			this->PWM_HI = (u8_t) (APBfq * 0.48) - 1;           // Log.1 - 48% - 0.60us
			this->PWM_LO = (u8_t) (APBfq * 0.24) - 1; break;    // Log.0 - 24% - 0.30us
	}

	this->ARGB_LOC_ST = ARGB_READY; // Set Ready Flag
	TIM_CCxChannelCmd(this->TimHandler.Instance, this->TimCH, TIM_CCx_ENABLE); // Enable GPIO to IDLE state
	HAL_Delay(1); // Make some delay

}


void ARGB_SetRGB(ARGB *this, u16_t i, u8_t r, u8_t g, u8_t b)
{
	// overflow protection
	if (i >= this->NumPixels)
	{
		u16_t _i = i / this->NumPixels;
		i -= _i * this->NumPixels;
	}

	// set brightness
	r /= 256 / ((u16_t) this->ARGB_BR + 1);
	g /= 256 / ((u16_t) this->ARGB_BR + 1);
	b /= 256 / ((u16_t) this->ARGB_BR + 1);


    // Subpixel chain order
	u8_t subp1, subp2, subp3;
	switch(Chip)
	{
		case SK6812:
		case WS2811F:
		case WS2811S:
			subp1 = r; subp2 = g; subp3 = b; break;
		default:
			subp1 = g; subp2 = r; subp3 = b; break;
	}

	// RGB or RGBW
	switch(Chip) // todo Нужно ли привидение типов здесь?
	{
		case SK6812:
			this->RGB_BUF[4 * i] = subp1; 	  		 // subpixel 1
			this->RGB_BUF[4 * i + 1] = subp2; 		 // subpixel 2
			this->RGB_BUF[4 * i + 2] = subp3; break; // subpixel 3
		default:
			this->RGB_BUF[3 * i] = subp1;     		 // subpixel 1
			this->RGB_BUF[3 * i + 1] = subp2; 		 // subpixel 2
			this->RGB_BUF[3 * i + 2] = subp3; break; // subpixel 3
	}

}
